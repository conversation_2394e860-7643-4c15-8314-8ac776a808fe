{"rustc": 1842507548689473721, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2040997289075261528, "path": 8487520173250820266, "deps": [[555019317135488525, "regex_automata", false, 3671757338514675796], [2779309023524819297, "aho_corasick", false, 6999580342028018741], [9408802513701742484, "regex_syntax", false, 144911963550326211], [15932120279885307830, "memchr", false, 11118620167408351143]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-ca5185afcd42585a\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
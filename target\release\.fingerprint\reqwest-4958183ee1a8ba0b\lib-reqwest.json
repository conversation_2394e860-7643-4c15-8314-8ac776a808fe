{"rustc": 1842507548689473721, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 7859547470675518382, "path": 8687871822414892605, "deps": [[40386456601120721, "percent_encoding", false, 5074771014479252742], [41016358116313498, "hyper_util", false, 11851883478913860291], [784494742817713399, "tower_service", false, 3577325964247015323], [1906322745568073236, "pin_project_lite", false, 4230724718521225101], [2054153378684941554, "tower_http", false, 16424196542077593459], [2517136641825875337, "sync_wrapper", false, 17911280945156104781], [2883436298747778685, "rustls_pki_types", false, 12909256937545239645], [3150220818285335163, "url", false, 1700620193673196115], [5695049318159433696, "tower", false, 8757218926948728662], [5986029879202738730, "log", false, 4268188471851143901], [7620660491849607393, "futures_core", false, 5371940579528272629], [9010263965687315507, "http", false, 9320379225202554334], [9689903380558560274, "serde", false, 17270949286662840186], [10229185211513642314, "mime", false, 1670034457853391160], [11957360342995674422, "hyper", false, 1874047541829966230], [12186126227181294540, "tokio_native_tls", false, 14140902317976038864], [13077212702700853852, "base64", false, 10981850954966665274], [14084095096285906100, "http_body", false, 15495320847413964727], [14359893265615549706, "h2", false, 18244270972437703107], [14564311161534545801, "encoding_rs", false, 2067029590857991361], [16066129441945555748, "bytes", false, 13415992542601879811], [16362055519698394275, "serde_json", false, 11308209756294985618], [16542808166767769916, "serde_urlencoded", false, 11128407217394331176], [16785601910559813697, "native_tls_crate", false, 10715414495494162563], [16900715236047033623, "http_body_util", false, 13472615204061332313], [17531218394775549125, "tokio", false, 8622598028916719679], [18273243456331255970, "hyper_tls", false, 5397127902566373451]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-4958183ee1a8ba0b\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}